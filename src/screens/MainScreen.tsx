import { useEffect } from 'react';
import { View, Text, TouchableOpacity, StatusBar, Animated } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAppSelector, useAppDispatch } from '../store/hooks';
import Header from '../components/Header';
import AccessibilityMenu from '../components/AccessibilityMenu';
import { loadAccessibilitySettings } from '../store/accessibilitySlice';

export default function MainScreen() {
  const dispatch = useAppDispatch();
  const {
    darkMode,
    lineHeight,
    letterSpacing,
    textAlign,
    contrast,
    highlightLinks,
    biggerText,
    textSpacing,
    pauseAnimations,
    dyslexia,
    cursor
  } = useAppSelector((state) => state.accessibility);

  const fadeAnim = new Animated.Value(1);

  useEffect(() => {
    loadSettings();
  }, []);

  useEffect(() => {
    saveSettings();
  }, [darkMode, lineHeight, letterSpacing, textAlign, contrast, highlightLinks, biggerText, textSpacing, pauseAnimations, dyslexia, cursor]);

  const loadSettings = async () => {
    try {
      const settings = await AsyncStorage.getItem('accessibilitySettings');
      if (settings) {
        dispatch(loadAccessibilitySettings(JSON.parse(settings)));
      }
    } catch (error) {
      console.error('Failed to load accessibility settings:', error);
    }
  };

  const saveSettings = async () => {
    try {
      const settings = {
        darkMode,
        lineHeight,
        letterSpacing,
        textAlign,
        contrast,
        highlightLinks,
        biggerText,
        textSpacing,
        pauseAnimations,
        dyslexia,
        cursor,
      };
      await AsyncStorage.setItem('accessibilitySettings', JSON.stringify(settings));
    } catch (error) {
      console.error('Failed to save accessibility settings:', error);
    }
  };

  const getTextStyle = () => {
    const baseSize = biggerText ? 1.2 : 1;
    const spacing = textSpacing ? letterSpacing + 1 : letterSpacing;
    
    return {
      lineHeight: lineHeight * 20,
      letterSpacing: spacing,
      textAlign: textAlign as any,
      fontSize: baseSize,
    };
  };

  const getBackgroundColors = (): [string, string, ...string[]] => {
    if (darkMode) {
      return contrast
        ? ['#000000', '#1a1a1a', '#333333']
        : ['#2d1b69', '#8b5cf6', '#a855f7'];
    }
    return contrast
      ? ['#ffffff', '#f3f4f6', '#e5e7eb']
      : ['#f3e8ff', '#e9d5ff', '#ddd6fe']; // Soft pink/lavender gradient
  };

  const getTextColor = () => {
    if (darkMode) {
      return contrast ? '#ffffff' : '#ffffff';
    }
    return contrast ? '#000000' : '#1f2937';
  };

  const getSecondaryTextColor = () => {
    if (darkMode) {
      return contrast ? '#d1d5db' : '#e5e7eb';
    }
    return contrast ? '#374151' : '#6b7280';
  };

  const getFontFamily = () => {
    return dyslexia ? 'System' : 'System';
  };

  const animateTransition = () => {
    if (!pauseAnimations) {
      Animated.sequence([
        Animated.timing(fadeAnim, {
          toValue: 0.8,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start();
    }
  };

  useEffect(() => {
    animateTransition();
  }, [darkMode, contrast, biggerText]);

  return (
    <View className="flex-1">
      <StatusBar barStyle={darkMode ? 'light-content' : 'dark-content'} />
      <LinearGradient
        colors={getBackgroundColors()}
        className="flex-1"
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
      >
        <Header />

        <Animated.View 
          className="flex-1 justify-center px-6"
          style={{ opacity: fadeAnim }}
        >
          {/* Logo and Brand */}
          <View className="items-center mb-16">
            {/* Logo */}
            <View className="flex-row items-center mb-8">
              <View className="w-10 h-10 bg-purple-600 rounded-lg mr-3 items-center justify-center">
                <Text className="text-white font-bold text-xl">🌟</Text>
              </View>
              <Text
                className="text-3xl font-bold"
                style={{
                  color: getTextColor(),
                  fontFamily: getFontFamily(),
                  ...getTextStyle(),
                }}
                accessibilityLabel="LiftUP AI Logo"
              >
                LiftUP AI
              </Text>
            </View>

            {/* Welcome Text */}
            <Text
              className="text-4xl font-bold mb-2 text-left"
              style={{
                color: getTextColor(),
                fontFamily: getFontFamily(),
                ...getTextStyle(),
              }}
              accessibilityLabel="Welcome message"
            >
              Welcome to
            </Text>
            
            <Text
              className="text-4xl font-bold mb-4 text-left"
              style={{
                color: '#8b5cf6',
                fontFamily: getFontFamily(),
                ...getTextStyle(),
              }}
              accessibilityLabel="LiftUP AI brand name"
            >
              LiftUP Ai
            </Text>

            <Text
              className="text-lg text-center"
              style={{
                color: getSecondaryTextColor(),
                fontFamily: getFontFamily(),
                ...getTextStyle(),
              }}
              accessibilityLabel="App description"
            >
              Your Smart Learning Companion!
            </Text>
          </View>

          {/* Action Buttons */}
          <View className="w-full space-y-4">
            {/* Get Started Button with Gradient */}
            <LinearGradient
              colors={['#fbbf24', '#f59e0b', '#8b5cf6', '#7c3aed']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              className="rounded-full"
            >
              <TouchableOpacity
                className="py-4 px-8 rounded-full"
                accessibilityLabel="Get Started button"
                accessibilityHint="Tap to begin using the app"
              >
                <View className="flex-row items-center justify-center">
                  <Text 
                    className="text-white font-semibold text-lg mr-2"
                    style={{
                      fontFamily: getFontFamily(),
                      fontSize: biggerText ? 20 : 18,
                    }}
                  >
                    Get Started
                  </Text>
                  <Text className="text-white">→</Text>
                </View>
              </TouchableOpacity>
            </LinearGradient>

            {/* Log In Button */}
            <TouchableOpacity
              className="rounded-full py-4 px-8"
              style={{
                backgroundColor: 'rgba(255, 255, 255, 0.9)',
              }}
              accessibilityLabel="Log in button"
              accessibilityHint="Tap to log into your account"
            >
              <Text
                className="text-center font-semibold text-lg text-gray-800"
                style={{
                  fontFamily: getFontFamily(),
                  fontSize: biggerText ? 20 : 18,
                }}
              >
                Log in
              </Text>
            </TouchableOpacity>
          </View>
        </Animated.View>

        <AccessibilityMenu />
      </LinearGradient>
    </View>
  );
}
