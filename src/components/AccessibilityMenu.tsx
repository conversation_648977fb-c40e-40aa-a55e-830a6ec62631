import { View, Text, TouchableOpacity, Modal, StyleSheet } from 'react-native';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import {
  increaseLineHeight,
  toggleTextAlign,
  toggleMenu,
  toggleContrast,
  toggleHighlightLinks,
  toggleBiggerText,
  toggleTextSpacing,
  togglePauseAnimations,
  toggleDyslexia,
  toggleCursor,
  resetAllAccessibility,
} from '../store/accessibilitySlice';

export default function AccessibilityMenu() {
  const dispatch = useAppDispatch();
  const { isMenuOpen } = useAppSelector((state) => state.accessibility);

  const accessibilityOptions = [
    {
      title: 'Contrast+',
      icon: '🌓',
      onPress: () => dispatch(toggleContrast()),
    },
    {
      title: 'Highlight Links',
      icon: '🔗',
      onPress: () => dispatch(toggleHighlightLinks()),
    },
    {
      title: 'Bigger Text',
      icon: 'T+',
      onPress: () => dispatch(toggleBiggerText()),
    },
    {
      title: 'Text Spacing',
      icon: '↔',
      onPress: () => dispatch(toggleTextSpacing()),
    },
    {
      title: 'Pause Animations',
      icon: '⏸',
      onPress: () => dispatch(togglePauseAnimations()),
    },
    {
      title: 'Dyslexia',
      icon: '🧠',
      onPress: () => dispatch(toggleDyslexia()),
    },
    {
      title: 'Cursor',
      icon: '↗',
      onPress: () => dispatch(toggleCursor()),
    },
    {
      title: 'Text Align',
      icon: '≡',
      onPress: () => dispatch(toggleTextAlign()),
    },
    {
      title: 'Line Height',
      icon: '≡↕',
      onPress: () => dispatch(increaseLineHeight()),
    },
  ];

  return (
    <Modal
      transparent={true}
      visible={isMenuOpen}
      animationType="slide"
      onRequestClose={() => dispatch(toggleMenu())}
    >
      <View className="flex-1 justify-end" style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}>
        <View
          className="rounded-t-3xl p-6"
          style={{
            backgroundColor: '#1a237e', // Dark blue background
          }}
        >
          {/* Header */}
          <View className="flex-row items-center justify-between mb-6">
            <Text
              style={{
                fontSize: 20,
                fontWeight: 'bold',
                color: '#ffffff', // White text
                fontFamily: 'System' // Use system font
              }}
            >
              Accessibility Menu
            </Text>
            <TouchableOpacity
              onPress={() => dispatch(toggleMenu())}
              className="w-8 h-8 rounded-full items-center justify-center"
              style={{ backgroundColor: '#3949ab' }} // Light blue for close button
              accessibilityLabel="Close accessibility menu"
            >
              <Text style={{ color: '#ffffff', fontWeight: 'bold', fontSize: 16 }}>✕</Text>
            </TouchableOpacity>
          </View>

          {/* Grid of Options - 3x3 layout */}
          <View style={styles.gridContainer}>
            {accessibilityOptions.map((option, index) => (
              <AccessibilityOption
                key={index}
                title={option.title}
                icon={option.icon}
                onPress={option.onPress}
              />
            ))}
          </View>

          {/* Reset Button - Centered below grid */}
          <View className="items-center mt-6">
            <TouchableOpacity
              onPress={() => dispatch(resetAllAccessibility())}
              className="rounded-full py-4 px-6 items-center"
              style={{
                backgroundColor: '#ff5722', // Red-orange color
                borderRadius: 25, // Rounded corners
              }}
              accessibilityLabel="Reset all accessibility settings"
            >
              <View className="flex-row items-center">
                <Text className="text-white font-semibold text-lg mr-2">🔄</Text>
                <Text
                  style={{
                    color: '#ffffff',
                    fontWeight: '600',
                    fontSize: 18,
                    fontFamily: 'System'
                  }}
                >
                  Reset All Accessibility
                </Text>
              </View>
            </TouchableOpacity>
          </View>

          {/* Bottom indicator */}
          <View className="items-center mt-4">
            <View
              className="rounded-full"
              style={{
                width: 134,
                height: 5,
                backgroundColor: '#ffffff', // White indicator
              }}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
}

interface AccessibilityOptionProps {
  title: string;
  icon: string;
  onPress: () => void;
}

function AccessibilityOption({ title, icon, onPress }: AccessibilityOptionProps) {
  return (
    <TouchableOpacity
      onPress={onPress}
      style={styles.optionButton}
      accessibilityLabel={title}
      accessibilityHint={`Toggle ${title} accessibility feature`}
    >
      <View className="w-8 h-8 mb-2 items-center justify-center">
        <Text style={{ fontSize: 24, color: '#ffffff' }}>{icon}</Text>
      </View>
      <Text
        style={styles.optionText}
        numberOfLines={2}
      >
        {title}
      </Text>
    </TouchableOpacity>
  );
}

// Styles object for the component
const styles = StyleSheet.create({
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 24,
    paddingHorizontal: 8,
  },
  optionButton: {
    width: '30%',
    marginBottom: 16,
    borderRadius: 16, // Rounded corners
    padding: 16,
    alignItems: 'center',
    backgroundColor: '#3949ab', // Light blue for buttons
    minHeight: 100,
    justifyContent: 'center',
    // Subtle shadow for depth
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  optionText: {
    textAlign: 'center',
    fontSize: 13,
    fontWeight: '600',
    color: '#ffffff', // White text
    fontFamily: 'System', // Use system font
    lineHeight: 18,
  },
});
