import { View, Text, TouchableOpacity, Modal } from 'react-native';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import {
  increaseLineHeight,
  toggleTextAlign,
  toggleMenu,
  toggleContrast,
  toggleHighlightLinks,
  toggleBiggerText,
  toggleTextSpacing,
  togglePauseAnimations,
  toggleDyslexia,
  toggleCursor,
  resetAllAccessibility,
} from '../store/accessibilitySlice';

export default function AccessibilityMenu() {
  const dispatch = useAppDispatch();
  const { isMenuOpen } = useAppSelector((state) => state.accessibility);

  const accessibilityOptions = [
    {
      title: 'Contrast+',
      icon: '🌓',
      onPress: () => dispatch(toggleContrast()),
    },
    {
      title: 'Highlight Links',
      icon: '🔗',
      onPress: () => dispatch(toggleHighlightLinks()),
    },
    {
      title: 'Bigger Text',
      icon: 'T+',
      onPress: () => dispatch(toggleBiggerText()),
    },
    {
      title: 'Text Spacing',
      icon: '↔',
      onPress: () => dispatch(toggleTextSpacing()),
    },
    {
      title: 'Pause Animations',
      icon: '⏸',
      onPress: () => dispatch(togglePauseAnimations()),
    },
    {
      title: 'Dyslexia',
      icon: '🧠',
      onPress: () => dispatch(toggleDyslexia()),
    },
    {
      title: 'Cursor',
      icon: '↗',
      onPress: () => dispatch(toggleCursor()),
    },
    {
      title: 'Text Align',
      icon: '≡',
      onPress: () => dispatch(toggleTextAlign()),
    },
    {
      title: 'Line Height',
      icon: '≡↕',
      onPress: () => dispatch(increaseLineHeight()),
    },
  ];

  return (
    <Modal
      transparent={true}
      visible={isMenuOpen}
      animationType="slide"
      onRequestClose={() => dispatch(toggleMenu())}
    >
      <View className="flex-1 justify-end" style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}>
        <View
          className="rounded-t-3xl p-6"
          style={{
            backgroundColor: 'rgba(255, 255, 255, 0.95)',
          }}
        >
          {/* Header */}
          <View className="flex-row items-center justify-between mb-6">
            <Text className="text-xl font-bold text-gray-800">
              Accessibility Menu
            </Text>
            <TouchableOpacity
              onPress={() => dispatch(toggleMenu())}
              className="w-8 h-8 rounded-full bg-gray-200 items-center justify-center"
              accessibilityLabel="Close accessibility menu"
            >
              <Text className="text-gray-600 font-bold">✕</Text>
            </TouchableOpacity>
          </View>

          {/* Grid of Options */}
          <View className="flex-row flex-wrap justify-between mb-6">
            {accessibilityOptions.map((option, index) => (
              <AccessibilityOption
                key={index}
                title={option.title}
                icon={option.icon}
                onPress={option.onPress}
              />
            ))}
          </View>

          {/* Reset Button */}
          <TouchableOpacity
            onPress={() => dispatch(resetAllAccessibility())}
            className="rounded-full py-4 px-6 items-center"
            style={{
              backgroundColor: '#8b5cf6',
            }}
            accessibilityLabel="Reset all accessibility settings"
          >
            <View className="flex-row items-center">
              <Text className="text-white font-semibold text-lg mr-2">🔄</Text>
              <Text className="text-white font-semibold text-lg">Reset All Accessibility</Text>
            </View>
          </TouchableOpacity>

          {/* Bottom indicator */}
          <View className="items-center mt-4">
            <View
              className="rounded-full"
              style={{
                width: 134,
                height: 5,
                backgroundColor: '#000000',
              }}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
}

interface AccessibilityOptionProps {
  title: string;
  icon: string;
  onPress: () => void;
}

function AccessibilityOption({ title, icon, onPress }: AccessibilityOptionProps) {
  return (
    <TouchableOpacity
      onPress={onPress}
      className="w-[30%] mb-4 rounded-2xl p-4 items-center"
      style={{
        backgroundColor: 'rgba(255, 255, 255, 0.8)',
        borderWidth: 1,
        borderColor: 'rgba(139, 92, 246, 0.2)',
      }}
      accessibilityLabel={title}
      accessibilityHint={`Toggle ${title} accessibility feature`}
    >
      <View className="w-8 h-8 mb-2 items-center justify-center">
        <Text className="text-2xl">{icon}</Text>
      </View>
      <Text
        className="text-center text-sm font-medium text-gray-700"
        numberOfLines={2}
      >
        {title}
      </Text>
    </TouchableOpacity>
  );
}
